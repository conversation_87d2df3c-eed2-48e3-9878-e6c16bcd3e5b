/**
 * 移动端滚动性能优化工具
 * 解决iPhone手机滚动卡顿问题
 */

export interface ScrollOptimizationOptions {
  /** 主容器选择器 */
  containerSelector?: string;
  /** 图表容器选择器 */
  chartSelector?: string;
  /** 表格容器选择器 */
  tableSelector?: string;
  /** 滚动结束延迟时间 */
  scrollEndDelay?: number;
  /** 是否启用调试模式 */
  debug?: boolean;
}

export class MobileScrollOptimizer {
  private options: Required<ScrollOptimizationOptions>;
  private scrollTimer: NodeJS.Timeout | null = null;
  private isScrolling = false;
  private cleanup: (() => void)[] = [];

  constructor(options: ScrollOptimizationOptions = {}) {
    this.options = {
      containerSelector: options.containerSelector || '',
      chartSelector: options.chartSelector || '.mobile-chart-container',
      tableSelector: options.tableSelector || '.mobile-table-container',
      scrollEndDelay: options.scrollEndDelay || 150,
      debug: options.debug || false,
      ...options,
    };
  }

  /**
   * 初始化滚动优化
   */
  public init(): () => void {
    // 只在移动端启用
    if (typeof window === 'undefined' || window.innerWidth > 768) {
      return () => {};
    }

    this.log('初始化移动端滚动优化');

    const containerEl = this.options.containerSelector 
      ? document.querySelector(this.options.containerSelector) as HTMLElement
      : document.body;
    
    const chartContainers = document.querySelectorAll(this.options.chartSelector);
    const tableContainers = document.querySelectorAll(this.options.tableSelector);

    // 滚动开始处理
    const handleScrollStart = () => {
      if (this.isScrolling) return;
      
      this.isScrolling = true;
      this.log('滚动开始 - 启用硬件加速');
      
      containerEl?.classList.add('scrolling');
      chartContainers.forEach(el => el.classList.add('chart-interacting'));
      tableContainers.forEach(el => el.classList.add('table-scrolling'));
    };

    // 滚动结束处理
    const handleScrollEnd = () => {
      if (this.scrollTimer) clearTimeout(this.scrollTimer);
      
      this.scrollTimer = setTimeout(() => {
        this.isScrolling = false;
        this.log('滚动结束 - 移除硬件加速');
        
        containerEl?.classList.remove('scrolling');
        chartContainers.forEach(el => el.classList.remove('chart-interacting'));
        tableContainers.forEach(el => el.classList.remove('table-scrolling'));
      }, this.options.scrollEndDelay);
    };

    // 滚动事件处理器
    const handleScroll = () => {
      handleScrollStart();
      handleScrollEnd();
    };

    // 触摸事件处理器 - 优化触摸响应
    const handleTouchStart = () => {
      this.log('触摸开始');
      handleScrollStart();
    };

    const handleTouchEnd = () => {
      this.log('触摸结束');
      handleScrollEnd();
    };

    // 添加事件监听器
    const scrollOptions = { passive: true };
    const touchOptions = { passive: true };

    window.addEventListener('scroll', handleScroll, scrollOptions);
    containerEl?.addEventListener('scroll', handleScroll, scrollOptions);
    containerEl?.addEventListener('touchstart', handleTouchStart, touchOptions);
    containerEl?.addEventListener('touchend', handleTouchEnd, touchOptions);

    // 存储清理函数
    this.cleanup.push(() => {
      window.removeEventListener('scroll', handleScroll);
      containerEl?.removeEventListener('scroll', handleScroll);
      containerEl?.removeEventListener('touchstart', handleTouchStart);
      containerEl?.removeEventListener('touchend', handleTouchEnd);
      if (this.scrollTimer) clearTimeout(this.scrollTimer);
    });

    // 返回清理函数
    return () => this.destroy();
  }

  /**
   * 销毁优化器
   */
  public destroy(): void {
    this.log('销毁滚动优化器');
    this.cleanup.forEach(fn => fn());
    this.cleanup = [];
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
      this.scrollTimer = null;
    }
  }

  /**
   * 调试日志
   */
  private log(message: string): void {
    if (this.options.debug) {
      console.log(`[MobileScrollOptimizer] ${message}`);
    }
  }
}

/**
 * 创建移动端滚动优化实例的便捷函数
 */
export function createMobileScrollOptimizer(options?: ScrollOptimizationOptions): MobileScrollOptimizer {
  return new MobileScrollOptimizer(options);
}

/**
 * Vue组合式函数 - 移动端滚动优化
 */
export function useMobileScrollOptimization(options?: ScrollOptimizationOptions) {
  let optimizer: MobileScrollOptimizer | null = null;
  let cleanup: (() => void) | null = null;

  const init = () => {
    if (optimizer) return;
    
    optimizer = new MobileScrollOptimizer(options);
    cleanup = optimizer.init();
  };

  const destroy = () => {
    if (cleanup) {
      cleanup();
      cleanup = null;
    }
    if (optimizer) {
      optimizer.destroy();
      optimizer = null;
    }
  };

  return {
    init,
    destroy,
  };
}

/**
 * 检测是否为移动设备
 */
export function isMobileDevice(): boolean {
  if (typeof window === 'undefined') return false;
  
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
         window.innerWidth <= 768;
}

/**
 * 检测是否为iOS设备
 */
export function isIOSDevice(): boolean {
  if (typeof window === 'undefined') return false;
  
  return /iPad|iPhone|iPod/.test(navigator.userAgent);
}

/**
 * 应用移动端滚动优化样式
 */
export function applyMobileScrollStyles(): void {
  if (!isMobileDevice()) return;

  const style = document.createElement('style');
  style.textContent = `
    /* 移动端滚动优化样式 */
    .mobile-scroll-optimized {
      -webkit-overflow-scrolling: touch;
      overscroll-behavior: contain;
      -webkit-overscroll-behavior: contain;
      contain: layout style paint;
      touch-action: pan-y;
    }
    
    .mobile-scroll-optimized.scrolling {
      transform: translateZ(0);
      -webkit-transform: translateZ(0);
      will-change: scroll-position;
    }
    
    .mobile-chart-optimized.chart-interacting {
      transform: translateZ(0);
      -webkit-transform: translateZ(0);
      will-change: transform;
      backface-visibility: hidden;
      -webkit-backface-visibility: hidden;
    }
    
    .mobile-table-optimized.table-scrolling {
      transform: translateZ(0);
      -webkit-transform: translateZ(0);
      will-change: scroll-position;
    }
    
    .mobile-table-optimized .ant-table-body::-webkit-scrollbar {
      display: none;
    }
  `;
  
  document.head.appendChild(style);
}
