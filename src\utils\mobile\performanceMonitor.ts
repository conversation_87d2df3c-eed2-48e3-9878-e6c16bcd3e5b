/**
 * 移动端性能监控工具
 * 用于监控和调试移动端滚动性能问题
 */

export interface PerformanceMetrics {
  fps: number;
  scrollEvents: number;
  memoryUsage?: number;
  renderTime: number;
  scrollLatency: number;
}

export interface PerformanceConfig {
  enableFPSMonitor?: boolean;
  enableScrollMonitor?: boolean;
  enableMemoryMonitor?: boolean;
  enableRenderMonitor?: boolean;
  sampleInterval?: number;
  maxSamples?: number;
  debug?: boolean;
}

export class MobilePerformanceMonitor {
  private config: Required<PerformanceConfig>;
  private metrics: PerformanceMetrics[] = [];
  private isMonitoring = false;
  private animationId: number | null = null;
  private lastFrameTime = 0;
  private frameCount = 0;
  private scrollEventCount = 0;
  private lastScrollTime = 0;
  private renderStartTime = 0;

  constructor(config: PerformanceConfig = {}) {
    this.config = {
      enableFPSMonitor: config.enableFPSMonitor ?? true,
      enableScrollMonitor: config.enableScrollMonitor ?? true,
      enableMemoryMonitor: config.enableMemoryMonitor ?? true,
      enableRenderMonitor: config.enableRenderMonitor ?? true,
      sampleInterval: config.sampleInterval ?? 1000,
      maxSamples: config.maxSamples ?? 60,
      debug: config.debug ?? false,
    };
  }

  /**
   * 开始性能监控
   */
  public start(): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.log('开始性能监控');

    if (this.config.enableFPSMonitor) {
      this.startFPSMonitor();
    }

    if (this.config.enableScrollMonitor) {
      this.startScrollMonitor();
    }

    if (this.config.enableRenderMonitor) {
      this.startRenderMonitor();
    }

    // 定期收集指标
    setInterval(() => {
      this.collectMetrics();
    }, this.config.sampleInterval);
  }

  /**
   * 停止性能监控
   */
  public stop(): void {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;
    this.log('停止性能监控');

    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }

    this.removeEventListeners();
  }

  /**
   * 获取性能指标
   */
  public getMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  /**
   * 获取平均性能指标
   */
  public getAverageMetrics(): PerformanceMetrics {
    if (this.metrics.length === 0) {
      return {
        fps: 0,
        scrollEvents: 0,
        memoryUsage: 0,
        renderTime: 0,
        scrollLatency: 0,
      };
    }

    const sum = this.metrics.reduce(
      (acc, metric) => ({
        fps: acc.fps + metric.fps,
        scrollEvents: acc.scrollEvents + metric.scrollEvents,
        memoryUsage: acc.memoryUsage + (metric.memoryUsage || 0),
        renderTime: acc.renderTime + metric.renderTime,
        scrollLatency: acc.scrollLatency + metric.scrollLatency,
      }),
      { fps: 0, scrollEvents: 0, memoryUsage: 0, renderTime: 0, scrollLatency: 0 }
    );

    const count = this.metrics.length;
    return {
      fps: sum.fps / count,
      scrollEvents: sum.scrollEvents / count,
      memoryUsage: sum.memoryUsage / count,
      renderTime: sum.renderTime / count,
      scrollLatency: sum.scrollLatency / count,
    };
  }

  /**
   * 清除性能数据
   */
  public clearMetrics(): void {
    this.metrics = [];
    this.frameCount = 0;
    this.scrollEventCount = 0;
  }

  /**
   * 开始FPS监控
   */
  private startFPSMonitor(): void {
    const measureFPS = (currentTime: number) => {
      if (this.lastFrameTime === 0) {
        this.lastFrameTime = currentTime;
      }

      this.frameCount++;

      if (this.isMonitoring) {
        this.animationId = requestAnimationFrame(measureFPS);
      }
    };

    this.animationId = requestAnimationFrame(measureFPS);
  }

  /**
   * 开始滚动监控
   */
  private startScrollMonitor(): void {
    const handleScroll = () => {
      const currentTime = performance.now();
      this.scrollEventCount++;
      
      if (this.lastScrollTime > 0) {
        const latency = currentTime - this.lastScrollTime;
        this.log(`滚动延迟: ${latency.toFixed(2)}ms`);
      }
      
      this.lastScrollTime = currentTime;
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    document.addEventListener('touchmove', handleScroll, { passive: true });
  }

  /**
   * 开始渲染监控
   */
  private startRenderMonitor(): void {
    // 监控DOM变化
    const observer = new MutationObserver(() => {
      this.renderStartTime = performance.now();
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
    });
  }

  /**
   * 收集性能指标
   */
  private collectMetrics(): void {
    if (!this.isMonitoring) return;

    const currentTime = performance.now();
    const timeDiff = currentTime - this.lastFrameTime;
    const fps = timeDiff > 0 ? (this.frameCount * 1000) / timeDiff : 0;

    const renderTime = this.renderStartTime > 0 ? currentTime - this.renderStartTime : 0;
    const scrollLatency = this.lastScrollTime > 0 ? currentTime - this.lastScrollTime : 0;

    const metrics: PerformanceMetrics = {
      fps: Math.round(fps),
      scrollEvents: this.scrollEventCount,
      renderTime: Math.round(renderTime),
      scrollLatency: Math.round(scrollLatency),
    };

    // 添加内存使用情况（如果支持）
    if (this.config.enableMemoryMonitor && 'memory' in performance) {
      const memory = (performance as any).memory;
      metrics.memoryUsage = Math.round(memory.usedJSHeapSize / 1024 / 1024); // MB
    }

    this.metrics.push(metrics);

    // 限制样本数量
    if (this.metrics.length > this.config.maxSamples) {
      this.metrics.shift();
    }

    // 重置计数器
    this.frameCount = 0;
    this.scrollEventCount = 0;
    this.lastFrameTime = currentTime;

    this.log(`性能指标: FPS=${metrics.fps}, 滚动事件=${metrics.scrollEvents}, 渲染时间=${metrics.renderTime}ms`);
  }

  /**
   * 移除事件监听器
   */
  private removeEventListeners(): void {
    // 这里应该移除所有添加的事件监听器
    // 由于我们使用了匿名函数，实际实现中应该保存引用
  }

  /**
   * 调试日志
   */
  private log(message: string): void {
    if (this.config.debug) {
      console.log(`[PerformanceMonitor] ${message}`);
    }
  }

  /**
   * 生成性能报告
   */
  public generateReport(): string {
    const avg = this.getAverageMetrics();
    const latest = this.metrics[this.metrics.length - 1];

    return `
移动端性能报告
==============
平均FPS: ${avg.fps.toFixed(1)}
平均滚动事件数: ${avg.scrollEvents.toFixed(1)}
平均渲染时间: ${avg.renderTime.toFixed(1)}ms
平均滚动延迟: ${avg.scrollLatency.toFixed(1)}ms
${avg.memoryUsage ? `平均内存使用: ${avg.memoryUsage.toFixed(1)}MB` : ''}

当前FPS: ${latest?.fps || 0}
当前滚动事件数: ${latest?.scrollEvents || 0}
当前渲染时间: ${latest?.renderTime || 0}ms
当前滚动延迟: ${latest?.scrollLatency || 0}ms
${latest?.memoryUsage ? `当前内存使用: ${latest.memoryUsage}MB` : ''}

性能建议:
${avg.fps < 30 ? '⚠️ FPS过低，建议优化渲染性能' : '✅ FPS正常'}
${avg.scrollLatency > 16 ? '⚠️ 滚动延迟过高，建议优化滚动处理' : '✅ 滚动延迟正常'}
${avg.renderTime > 16 ? '⚠️ 渲染时间过长，建议优化DOM操作' : '✅ 渲染时间正常'}
${avg.memoryUsage && avg.memoryUsage > 100 ? '⚠️ 内存使用过高，建议检查内存泄漏' : '✅ 内存使用正常'}
    `.trim();
  }
}

/**
 * 创建性能监控实例
 */
export function createPerformanceMonitor(config?: PerformanceConfig): MobilePerformanceMonitor {
  return new MobilePerformanceMonitor(config);
}

/**
 * 快速性能检测
 */
export function quickPerformanceCheck(): Promise<PerformanceMetrics> {
  return new Promise((resolve) => {
    const monitor = new MobilePerformanceMonitor({
      sampleInterval: 1000,
      maxSamples: 1,
      debug: false,
    });

    monitor.start();

    setTimeout(() => {
      monitor.stop();
      const metrics = monitor.getMetrics();
      resolve(metrics[0] || {
        fps: 0,
        scrollEvents: 0,
        memoryUsage: 0,
        renderTime: 0,
        scrollLatency: 0,
      });
    }, 1000);
  });
}
