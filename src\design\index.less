@import url('/src/design/transition/index.less');
@import url('/src/design/var/index.less');
@import url('/src/design/public.less');
@import url('/src/design/mobile-table-optimization.less');
@import url('/src/design/ant/index.less');
@import url('/src/design/theme.less');

input:-webkit-autofill {
  box-shadow: 0 0 0 1000px white inset !important;
}

:-webkit-autofill {
  transition: background-color 5000s ease-in-out 0s !important;
}

html {
  overflow: hidden;
  text-size-adjust: 100%;
  /* iOS Safari 滚动优化 */
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

html,
body {
  width: 100%;
  height: 100%;
  overflow: visible !important;
  overflow-x: hidden !important;
  /* 移动端滚动优化 */
  -webkit-overflow-scrolling: touch;
  /* 启用硬件加速 */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  /* 优化触摸滚动 */
  touch-action: pan-y;

  &.color-weak {
    filter: invert(80%);
  }

  &.gray-mode {
    filter: grayscale(100%);
    filter: progid:dximagetransform.microsoft.basicimage(grayscale=1);
  }
}

a:focus,
a:active,
button,
div,
svg,
span {
  outline: none !important;
}